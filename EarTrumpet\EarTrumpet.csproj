﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{BA3C7B42-84B0-468C-8640-217E2A24CF81}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>EarTrumpet</RootNamespace>
    <AssemblyName>EarTrumpet</AssemblyName>
    <TargetFrameworkVersion>v4.5</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <ProjectTypeGuids>{60dc8134-eba5-43b8-bcc9-bb4bc16c2548};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationIcon>Application.ico</ApplicationIcon>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x86'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>..\Build\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>true</Prefer32Bit>
    <WarningLevel>4</WarningLevel>
    <WarningsAsErrors>
    </WarningsAsErrors>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x86'">
    <OutputPath>..\Build\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>true</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Xaml">
      <RequiredTargetFramework>4.0</RequiredTargetFramework>
    </Reference>
    <Reference Include="WindowsBase" />
    <Reference Include="PresentationCore" />
    <Reference Include="PresentationFramework" />
  </ItemGroup>
  <ItemGroup>
    <ApplicationDefinition Include="App.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </ApplicationDefinition>
    <Compile Include="Extensions\DoubleExtensions.cs" />
    <Compile Include="Extensions\BlurWindowExtensions.cs" />
    <Compile Include="Extensions\IconExtensions.cs" />
    <Compile Include="Extensions\InvertableVisibilityConverter.cs" />
    <Compile Include="Extensions\OpacityConverter.cs" />
    <Compile Include="Extensions\SliderExtensions.cs" />
    <Compile Include="Models\EarTrumpetAudioDeviceModel.cs" />
    <Compile Include="Properties\Resources.de.Designer.cs">
      <DependentUpon>Resources.de.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Properties\Resources.nn-no.Designer.cs">
      <DependentUpon>Resources.nn-no.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Properties\Resources.nb-no.Designer.cs">
      <DependentUpon>Resources.nb-no.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="Services\AccentColorService.cs" />
    <Compile Include="Models\EarTrumpetAudioSessionModelGroup.cs" />
    <Compile Include="Services\EarTrumpetAudioDeviceService.cs" />
    <Compile Include="Services\TaskbarService.cs" />
    <Compile Include="Services\ThemeService.cs" />
    <Compile Include="Services\UserPreferencesService.cs" />
    <Compile Include="Services\UserSystemPreferencesService.cs" />
    <Compile Include="TrayIcon.cs" />
    <Compile Include="ViewModels\AppItemViewModel.cs" />
    <Compile Include="Extensions\CollectionExtensions.cs" />
    <Compile Include="Models\EarTrumpetAudioSessionModel.cs" />
    <Compile Include="Services\EarTrumpetAudioSessionService.cs" />
    <Compile Include="Extensions\WindowExtensions.cs" />
    <Compile Include="ViewModels\AppItemViewModelComparer.cs" />
    <Compile Include="ViewModels\AudioMixerViewModel.cs" />
    <Compile Include="ViewModels\BindableBase.cs" />
    <Compile Include="ViewModels\DeviceAppItemViewModel.cs" />
    <Compile Include="ViewModels\IAudioMixerViewModelCallback.cs" />
    <Page Include="MainWindow.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Compile Include="App.xaml.cs">
      <DependentUpon>App.xaml</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="MainWindow.xaml.cs">
      <DependentUpon>MainWindow.xaml</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Properties\AssemblyInfo.cs">
      <SubType>Code</SubType>
    </Compile>
    <AppDesigner Include="Properties\" />
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Application.ico" />
    <Resource Include="Tray.ico" />
    <None Include="Color Types.txt" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Properties\Resources.de.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.de.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.nn-no.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.nn-no.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.nb-no.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.nb-no.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.fr.resx" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>