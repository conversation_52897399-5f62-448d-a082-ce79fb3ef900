using System.Reflection;
using System.Runtime.InteropServices;
using System.Windows;

[assembly: AssemblyT<PERSON><PERSON>("Ear Trumpet")]
[assembly: AssemblyDescription("")]
[assembly: AssemblyConfiguration("")]
[assembly: AssemblyCompany("File-New-Project")]
[assembly: AssemblyProduct("Ear Trumpet")]
[assembly: AssemblyCopyright("")]
[assembly: AssemblyTrademark("")]
[assembly: AssemblyCulture("")]
[assembly: AssemblyVersion("*******")]
[assembly: AssemblyFileVersion("*******")]
[assembly: ComVisible(false)]
[assembly: ThemeInfo(ResourceDictionaryLocation.None, ResourceDictionaryLocation.SourceAssembly)]
