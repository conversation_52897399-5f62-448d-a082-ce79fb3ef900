﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace EarTrumpet.Properties {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "4.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class Resources {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Resources() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("EarTrumpet.Properties.Resources", typeof(Resources).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to About.
        /// </summary>
        public static string ContextMenuAboutTitle {
            get {
                return ResourceManager.GetString("ContextMenuAboutTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Exit.
        /// </summary>
        public static string ContextMenuExitTitle {
            get {
                return ResourceManager.GetString("ContextMenuExitTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No devices found.
        /// </summary>
        public static string ContextMenuNoDevices {
            get {
                return ResourceManager.GetString("ContextMenuNoDevices", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Show Desktop Apps.
        /// </summary>
        public static string ContextMenuShowDesktopAppsTitle {
            get {
                return ResourceManager.GetString("ContextMenuShowDesktopAppsTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to It doesn&apos;t look like you have any apps open..
        /// </summary>
        public static string NoAppsPanelContent {
            get {
                return ResourceManager.GetString("NoAppsPanelContent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to It doesn&apos;t look like you have any audio devices..
        /// </summary>
        public static string NoDevicesPanelContent {
            get {
                return ResourceManager.GetString("NoDevicesPanelContent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Speech Runtime.
        /// </summary>
        public static string SpeechRuntimeDisplayName {
            get {
                return ResourceManager.GetString("SpeechRuntimeDisplayName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to System Sounds.
        /// </summary>
        public static string SystemSoundsDisplayName {
            get {
                return ResourceManager.GetString("SystemSoundsDisplayName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Volume Control for Windows.
        /// </summary>
        public static string TrayIconTooltipText {
            get {
                return ResourceManager.GetString("TrayIconTooltipText", resourceCulture);
            }
        }
    }
}
