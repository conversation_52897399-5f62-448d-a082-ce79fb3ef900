﻿<Window x:Class="EarTrumpet.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:p="clr-namespace:EarTrumpet.Properties"
        xmlns:viewModels="clr-namespace:EarTrumpet.ViewModels"
        xmlns:ext="clr-namespace:EarTrumpet.Extensions"
        Title="Ear Trumpet"
        MaxHeight="600"
        Width="360"
        Icon="Application.ico"
        Topmost="True"
        ShowInTaskbar="False"
        AllowsTransparency="True"
        SnapsToDevicePixels="True"
        UseLayoutRounding="True"
        WindowStyle="None"
        Deactivated="Window_Deactivated"
        PreviewKeyDown="Window_PreviewKeyDown"
        Foreground="{DynamicResource WindowForeground}"
        Background="{DynamicResource WindowBackground}"
		ResizeMode="NoResize"
        x:Name="VolumeWindow">
    <Window.Resources>
        <SolidColorBrush x:Key="WindowForeground"
                         Color="Black" />
        <SolidColorBrush x:Key="WindowBackground"
                         Color="White" />
        <SolidColorBrush x:Key="CottonSwabSliderTrackBackground"
                         Color="#39FFFFFF" />
        <SolidColorBrush x:Key="CottonSwabSliderThumb"
                         Color="#FF339933" />
        <SolidColorBrush x:Key="HeaderBackground"
                         Color="#FFFFFFFF" />
        <SolidColorBrush x:Key="CottonSwabSliderThumbHover"
                         Color="Red" />
        <SolidColorBrush x:Key="CottonSwabSliderThumbPressed"
                         Color="Yellow" />
        <SolidColorBrush x:Key="CottonSwabSliderTrackFill"
                         Color="#FF339933" />
        <ext:OpacityConverter x:Key="opacityConverter" />
        <ext:InvertableVisibilityConverter x:Key="visConverter" />

        <Style x:Key="SliderButtonStyle"
               TargetType="{x:Type RepeatButton}">
            <Setter Property="OverridesDefaultStyle"
                    Value="true" />
            <Setter Property="IsTabStop"
                    Value="false" />
            <Setter Property="Focusable"
                    Value="false" />
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="{x:Type RepeatButton}">
                        <Border Background="Transparent">
                            <Border Background="{TemplateBinding Foreground}"
                                    Height="2">
                            </Border>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="SliderThumbStyle"
               TargetType="{x:Type Thumb}">
            <Setter Property="OverridesDefaultStyle"
                    Value="true" />
            <Setter Property="Height"
                    Value="24" />
            <Setter Property="Width"
                    Value="8" />
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="{x:Type Thumb}">
                        <Border Name="Thumb"
                                Background="{TemplateBinding Foreground}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="0"
                                CornerRadius="5">
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <ControlTemplate x:Key="HorizontalSlider"
                         TargetType="{x:Type Slider}">
            <Grid>
                <Border Name="TrackBackground"
                        Margin="0"
                        Height="2"
                        Background="{DynamicResource CottonSwabSliderTrackBackground}" />
                <Track Name="PART_Track">
                    <Track.DecreaseRepeatButton>
                        <RepeatButton Name="SliderLeft"
                                      Foreground="{DynamicResource CottonSwabSliderTrackFill}"
                                      Style="{StaticResource SliderButtonStyle}"
                                      Command="Slider.DecreaseLarge" />
                    </Track.DecreaseRepeatButton>
                    <Track.Thumb>
                        <Thumb Name="SliderThumb"
                               Foreground="{DynamicResource CottonSwabSliderThumb}"
                               Style="{StaticResource SliderThumbStyle}" />
                    </Track.Thumb>
                    <Track.IncreaseRepeatButton>
                        <RepeatButton Name="SliderRight"
                                      Foreground="{DynamicResource CottonSwabSliderTrackBackground}"
                                      Style="{StaticResource SliderButtonStyle}"
                                      Command="Slider.IncreaseLarge" />
                    </Track.IncreaseRepeatButton>
                </Track>
                <VisualStateManager.VisualStateGroups>
                    <VisualStateGroup x:Name="CommonStates">
                        <VisualStateGroup.Transitions>
                            <VisualTransition To="Normal"
                                              GeneratedDuration="0" />
                        </VisualStateGroup.Transitions>
                        <VisualState x:Name="Normal" />
                        <VisualState x:Name="MouseOver">
                            <Storyboard>
                                <ObjectAnimationUsingKeyFrames Storyboard.TargetProperty="Foreground"
                                                               Storyboard.TargetName="SliderThumb">
                                    <DiscreteObjectKeyFrame KeyTime="0"
                                                            Value="{StaticResource CottonSwabSliderThumbHover}" />
                                </ObjectAnimationUsingKeyFrames>
                            </Storyboard>
                        </VisualState>
                        <VisualState x:Name="Pressed">
                            <Storyboard>
                                <ObjectAnimationUsingKeyFrames Storyboard.TargetProperty="Foreground"
                                                               Storyboard.TargetName="SliderThumb">
                                    <DiscreteObjectKeyFrame KeyTime="0"
                                                            Value="{StaticResource CottonSwabSliderThumbPressed}" />
                                </ObjectAnimationUsingKeyFrames>
                            </Storyboard>
                        </VisualState>
                    </VisualStateGroup>
                </VisualStateManager.VisualStateGroups>
            </Grid>
            <ControlTemplate.Triggers>
                <Trigger Property="IsEnabled"
                         Value="False">
                    <Setter Property="Opacity"
                            Value="0.5" />
                </Trigger>
            </ControlTemplate.Triggers>
        </ControlTemplate>

        <Style TargetType="{x:Type Slider}">
            <Setter Property="OverridesDefaultStyle"
                    Value="true" />
            <Style.Triggers>
                <Trigger Property="Orientation"
                         Value="Horizontal">
                    <Setter Property="Height"
                            Value="32" />
                    <Setter Property="Template"
                            Value="{StaticResource HorizontalSlider}" />
                </Trigger>
            </Style.Triggers>
        </Style>
    </Window.Resources>

    <ScrollViewer x:Name="LayoutRoot"
                    VerticalScrollBarVisibility="Auto"
                    CanContentScroll="True"
                    PanningMode="VerticalOnly"
                    Focusable="False"                      
                    Margin="0">
        <HeaderedItemsControl ItemsSource="{Binding Apps, Mode=OneWay}"
                        Header="{Binding Device}"
                        ScrollViewer.HorizontalScrollBarVisibility="Disabled"
                        HorizontalContentAlignment="Stretch"
                        Focusable="False">
            <HeaderedItemsControl.Template>
                <ControlTemplate TargetType="HeaderedItemsControl">
                    <Grid x:Name="Root">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="*" />
                        </Grid.RowDefinitions>
                        <ContentControl Content="{TemplateBinding Header}"  ContentTemplate="{TemplateBinding HeaderTemplate}" HorizontalAlignment="Stretch" Visibility="{Binding DeviceVisibility}" />
                        <ItemsPresenter x:Name="Items" Grid.Row="1" Margin="0,0,0,12" Visibility="{Binding ListVisibility}" />
                        <Grid x:Name="NoAppsPanel"
                                Margin="0,8"
                                Grid.Row="1" 
                                Height="50"
                                Visibility="{Binding NoAppsPaneVisibility}">
                            <TextBlock Text="{Binding NoItemsContent}"
                                    VerticalAlignment="Center"
                                    HorizontalAlignment="Center"
                                    FontSize="{DynamicResource {x:Static SystemFonts.MessageFontSizeKey}}"
                                    TextWrapping="Wrap" />
                        </Grid>
                    </Grid>
                </ControlTemplate>
            </HeaderedItemsControl.Template>
            <HeaderedItemsControl.HeaderTemplate>
                <DataTemplate DataType="viewModels:DeviceAppItemViewModel">
                    <Border Background="{DynamicResource HeaderBackground}" BorderThickness="0,0,0,12">
                    <Grid Margin="12" HorizontalAlignment="Stretch">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="42" />
                        </Grid.ColumnDefinitions>
                        <Border Grid.Column="0"                                                                                                            
                                HorizontalAlignment="Center"
                                Height="32"
                                Width="32"
                                Margin="4,-2,4,0"
                                MouseDown="Icon_MouseDown"
                                Visibility="{Binding IsMuted, Converter={StaticResource visConverter}, ConverterParameter=Inverted}">
                                <TextBlock Text="&#xE767;" 
                                            FontFamily="Segoe MDL2 Assets" 
                                            FontSize="24" 
                                            VerticalAlignment="Center" 
                                            HorizontalAlignment="Center" 
                                            ToolTip="{Binding DisplayName}" />
                        </Border>
                        <TextBlock Grid.Column="0" 
                                    MouseDown="Mute_MouseDown" 
                                    Visibility="{Binding IsMuted, Converter={StaticResource visConverter}, ConverterParameter=Normal}" 
                                    Text="&#xE74F;" 
                                    FontFamily="Segoe MDL2 Assets" 
                                    FontSize="24" 
                                    HorizontalAlignment="Center" 
                                    VerticalAlignment="Center"
                                    ToolTip="{Binding DisplayName}" 
                                    Margin="0,-2,0,0" />

                        <Slider Grid.Column="1"
                                Value="{Binding Volume,Mode=TwoWay}"
                                AutomationProperties.Name="{Binding DisplayName, Mode=OneTime}"
                                Minimum="0"
                                Maximum="100"
                                VerticalContentAlignment="Center"
                                HorizontalContentAlignment="Stretch"
                                Margin="12,0"
                                HorizontalAlignment="Stretch"
                                VerticalAlignment="Center"
                                Interval="1"
                                SnapsToDevicePixels="True"
                                SmallChange="1"
                                LargeChange="10"
                                IsManipulationEnabled="True"
                                TouchDown="Slider_TouchDown"
                                TouchUp="Slider_TouchUp_1"
                                TouchMove="Slider_TouchMove"
                                PreviewMouseDown="Slider_MouseDown"
                                PreviewMouseUp="Slider_PreviewMouseUp"
                                MouseWheel="Slider_MouseWheel"
                                MouseMove="Slider_MouseMove"
                                Opacity="{Binding IsMuted, Converter={StaticResource opacityConverter}}" />
                        <TextBlock Grid.Column="2"
                                    Text="{Binding Volume, Mode=OneWay}"
                                    FontSize="24"
                                    FontFamily="Segoe UI"
                                    FontWeight="Normal"
                                    HorizontalAlignment="Center"
                                    TextAlignment="Center"
                                    Opacity="{Binding IsMuted, Converter={StaticResource opacityConverter}}" 
                                    Margin="0,-3,0,0" />
                    </Grid>
                    </Border>
                </DataTemplate>
            </HeaderedItemsControl.HeaderTemplate>
            <ItemsControl.ItemTemplate>
                <DataTemplate DataType="viewModels:AppItemViewModel">
                    <Grid Margin="12,6">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="42" />
                        </Grid.ColumnDefinitions>
                        <Border Grid.Column="0"                                    
                                ToolTip="{Binding DisplayName}"
                                Background="{Binding Background, Mode=OneTime}"
                                HorizontalAlignment="Center"
                                Height="32"
                                Width="32"
                                Margin="4,0"
                                MouseDown="Icon_MouseDown"
                                Opacity="{Binding IsMuted, Converter={StaticResource opacityConverter}}">
                            <Grid>
                                <Image Source="{Binding Icon, Mode=OneTime}"
                                        Stretch="Fill"
                                        Height="{Binding IconHeight, Mode=OneTime}"
                                        Width="{Binding IconWidth, Mode=OneTime}" />
                                    
                            </Grid>
                        </Border>
                        <TextBlock Grid.Column="0" 
                                    MouseDown="Mute_MouseDown" 
                                    Visibility="{Binding IsMuted, Converter={StaticResource visConverter}, ConverterParameter=Normal}" 
                                    Text="&#xE74F;" 
                                    FontFamily="Segoe MDL2 Assets" 
                                    FontSize="24" 
                                    HorizontalAlignment="Center" 
                                    VerticalAlignment="Center"
                                    ToolTip="{Binding DisplayName}" 
                                    Margin="0,-2,0,0" />

                        <Slider Grid.Column="1"
                                Value="{Binding Volume,Mode=TwoWay}"
                                AutomationProperties.Name="{Binding DisplayName, Mode=OneTime}"
                                Minimum="0"
                                Maximum="100"
                                VerticalContentAlignment="Center"
                                HorizontalContentAlignment="Stretch"
                                Margin="12,0"
                                VerticalAlignment="Center"
                                Interval="1"
                                SnapsToDevicePixels="True"
                                SmallChange="1"
                                LargeChange="10"
                                IsManipulationEnabled="True"
                                TouchDown="Slider_TouchDown"
                                TouchUp="Slider_TouchUp"
                                TouchMove="Slider_TouchMove"
                                PreviewMouseDown="Slider_MouseDown"
                                PreviewMouseUp="Slider_MouseUp"
                                MouseWheel="Slider_MouseWheel"
                                MouseMove="Slider_MouseMove"
                                Opacity="{Binding IsMuted, Converter={StaticResource opacityConverter}}" />
                        <TextBlock Grid.Column="2"
                                    Text="{Binding Volume, Mode=OneWay}"
                                    FontSize="24"
                                    FontFamily="Segoe UI"
                                    FontWeight="Normal"
                                    HorizontalAlignment="Center"
                                    TextAlignment="Center"
                                    VerticalAlignment="Center"
                                    Opacity="{Binding IsMuted, Converter={StaticResource opacityConverter}}" 
                                    Margin="0,-3,0,0" />
                    </Grid>
                </DataTemplate>
            </ItemsControl.ItemTemplate>
        </HeaderedItemsControl>
    </ScrollViewer>
</Window>
